import express from "express";
import cors from "cors";
import helmet from "helmet";
import morgan from "morgan";
import songsRoutes from "./routes/songs";

const app = express();
const PORT = process.env.PORT || 5000;

app.set("trust proxy", 1);

// Basic middleware
app.use(helmet());
app.use(cors());
app.use(morgan("combined"));
app.use(express.json());
app.use(express.urlencoded({ extended: true }));

// Routes
app.use("/api/songs", songsRoutes);

// Health check endpoint
app.get("/api/health", (req, res) => {
    res.json({
        status: "OK",
        message: "Song Management API is running",
        timestamp: new Date().toISOString(),
        deployment: {
            platform: "Render",
            environment: process.env.NODE_ENV || "production",
            version: "1.0.0",
        },
    });
});

// 404 handler
project-addis/backend on  main [!?] is 󰏗 v1.0.0 via  v22.17.0 
❯ pnpm run dev

> project-addis-backend@1.0.0 dev /home/<USER>/Documents/Development/Projects/Web Projects/project-addis/backend
> nodemon --exec ts-node src/server.ts

[nodemon] 3.1.10
[nodemon] to restart at any time, enter `rs`
[nodemon] watching path(s): *.*
[nodemon] watching extensions: ts,json
[nodemon] starting `ts-node src/server.ts`
/home/<USER>/Documents/Development/Projects/Web Projects/project-addis/backend/node_modules/.pnpm/path-to-regexp@8.2.0/node_modules/path-to-regexp/src/index.ts:153
      throw new TypeError(`Missing parameter name at ${i}: ${DEBUG_URL}`);
            ^
TypeError: Missing parameter name at 2: https://git.new/pathToRegexpError
    at name (/home/<USER>/Documents/Development/Projects/Web Projects/project-addis/backend/node_modules/.pnpm/path-to-regexp@8.2.0/node_modules/path-to-regexp/src/index.ts:153:13)
    at lexer (/home/<USER>/Documents/Development/Projects/Web Projects/project-addis/backend/node_modules/.pnpm/path-to-regexp@8.2.0/node_modules/path-to-regexp/src/index.ts:171:21)
    at lexer.next (<anonymous>)
    at Iter.peek (/home/<USER>/Documents/Development/Projects/Web Projects/project-addis/backend/node_modules/.pnpm/path-to-regexp@8.2.0/node_modules/path-to-regexp/src/index.ts:188:32)
    at Iter.tryConsume (/home/<USER>/Documents/Development/Projects/Web Projects/project-addis/backend/node_modules/.pnpm/path-to-regexp@8.2.0/node_modules/path-to-regexp/src/index.ts:195:24)
    at Iter.text (/home/<USER>/Documents/Development/Projects/Web Projects/project-addis/backend/node_modules/.pnpm/path-to-regexp@8.2.0/node_modules/path-to-regexp/src/index.ts:213:26)
    at consume (/home/<USER>/Documents/Development/Projects/Web Projects/project-addis/backend/node_modules/.pnpm/path-to-regexp@8.2.0/node_modules/path-to-regexp/src/index.ts:285:23)
    at parse (/home/<USER>/Documents/Development/Projects/Web Projects/project-addis/backend/node_modules/.pnpm/path-to-regexp@8.2.0/node_modules/path-to-regexp/src/index.ts:320:18)
    at /home/<USER>/Documents/Development/Projects/Web Projects/project-addis/backend/node_modules/.pnpm/path-to-regexp@8.2.0/node_modules/path-to-regexp/src/index.ts:503:40
    at Array.map (<anonymous>)
[nodemon] app crashed - waiting for file changes before starting...

// Error handling middleware
app.use(
    (
        err: any,
        req: express.Request,
        res: express.Response,
        next: express.NextFunction
    ) => {
        console.error(err.stack);
        res.status(500).json({
            error: "Internal Server Error",
            message: "Something went wrong!",
        });
    }
);

// Start server
app.listen(PORT, () => {
    console.log(`🎵 Song Management API server is running on port ${PORT}`);
    console.log(`📍 Health check: http://localhost:${PORT}/api/health`);
    console.log(`🎶 Songs API: http://localhost:${PORT}/api/songs`);
});

export default app;
